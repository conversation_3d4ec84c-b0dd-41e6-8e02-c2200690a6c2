#include "oled_bsp.h"
#include "key_bsp.h"  // 包含key_bsp.h以访问task和sure变量
#include "oled.h"
#include "gray_bsp.h"  // 包含循迹模块相关声明

// 全局变量：OLED显示的数字

// ���� OLED ����Ϊ 128 ���أ�ʹ�� 6x8 ����
// ÿ�� 8 ���ظߣ���� 64/8 = 8 �� (y=0~7) �� 32/8 = 4 �� (y=0~3)
// ÿ�� 6 ���ؿ������ 128/6 = 21 ���ַ� (x=0~20? ��������ܻ�������λ��)
// **ע��:** Oled_Printf �� x, y ������λ��Ҫ�ο� OLED_ShowStr ʵ�֣��������ַ�λ�û�����λ��
// �ĵ��е�ע�� (0-127, 0-3) ��ʾ������ 128x32 ��Ļ������ x ������ַ��� y ����

/**
 * @brief	ʹ������printf�ķ�ʽ��ʾ�ַ�������ʾ6x8��С��ASCII�ַ�
 * @param x  ��ʼ X ���� (����) �� �ַ���λ�� (��Ҫ�� OLED_ShowStr)
 * @param y  ��ʼ Y ���� (����) �� �ַ���λ�� (��Ҫ�� OLED_ShowStr, 0-3 �� 0-7)
 * @param format, ... ��ʽ���ַ���������
 * ���磺Oled_Printf(0, 0, "Data = %d", dat);
**/
int oled_printf(uint8_t x, uint8_t y, const char *format, ...)
{
  char buffer[512]; // 临时存储格式化后的字符串
  va_list arg;      // 处理可变参数
  int len;          // 最终字符串长度

  va_start(arg, format);
  // 安全地格式化字符串到 buffer
  len = vsnprintf(buffer, sizeof(buffer), format, arg);
  va_end(arg);

  OLED_ShowStr(x, y, buffer, 8);
  return len;
}



void oled_task(void) 
{

  oled_printf(0, 0, "task: %d", task);
	oled_printf(0, 1, "sure: %d", sure);

}
