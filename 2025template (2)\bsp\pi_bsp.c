#include "pi_bsp.h"
#include "step_motor_bsp.h"
// Ĭ��ֵ��Ϊ��Ч״̬��X, Y Ϊ 0
LaserCoord_t J_G = {J_G_ID, 0, 0, 0};
LaserCoord_t J_x = {J_X_ID, 0, 0, 0};

unsigned char i=0;



int pi_parse_data(char *buffer)
{
    if (!buffer)
        return -1; // 空指针检查

    int parsed_x, parsed_y; // 临时变量用于存储解析出的X,Y坐标
    int parsed_count;

    // 尝试匹配 "red:(x,y)" 格式
    if (strncmp(buffer, "light:", 6) == 0)
    {
        parsed_count = sscanf(buffer, "light:(%d,%d)", &parsed_x, &parsed_y);
        if (parsed_count != 2) // 必须解析出X和Y两个值
            return -2; // 解析失败

        // 解析成功，更新全局红色激光坐标
        J_G.x = parsed_x;
        J_G.y = parsed_y;
        J_G.isValid = 1; // 标记数据为有效

        // 打印调试信息
		my_printf(&huart1, "Parsed light: X=%d, Y=%d\r\n", J_G.x, J_G.y);
    }
    // 尝试匹配 "gre:(x,y)" 格式
    else if (strncmp(buffer, "juxing:", 7) == 0)
    {
        parsed_count = sscanf(buffer, "juxing:(%d,%d)", &parsed_x, &parsed_y);
        if (parsed_count != 2) // 必须解析出X和Y两个值
            return -2; // 解析失败

        // 解析成功，更新全局绿色激光坐标
        J_x.x = parsed_x;
        J_x.y = parsed_y;
        J_x.isValid = 1; // 标记数据为有效

        // 打印调试信息
		my_printf(&huart1, "Parsed juxing: X=%d, Y=%d\r\n", J_x.x, J_x.y);
    }
    else
    {
        // 既不是 "red:" 也不是 "gre:" 开头，认为是未知格式或无效数据
        return -3; // 未知或无效格式
    }

    return 0; // 成功
}


void pi_proc(void)
{

//	  float pos_out_x,pos_out_y=0;


//		pos_out_x = pid_calc(&pid_x,J_G.x, J_x.x, 0);
//		pos_out_y = pid_calc(&pid_y,J_G.y, J_x.y, 0);
//		Step_Motor_Set_Speed_my(-pos_out_x,pos_out_y);
////Step_Motor_Set_Speed_my(10,10);
if(i==0)
{
    Step_Motor_Set_Pwm_Fast(0,1600,3000);
	  i++;
}



}
