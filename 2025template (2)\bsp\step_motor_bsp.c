#include "step_motor_bsp.h"

/**
 * @brief �����ʼ������
 */
void Step_Motor_Init(void)
{
    /* ʹ��X���� */
    Emm_V5_En_Control(&MOTOR_X_UART, MOTOR_X_ADDR, true, MOTOR_SYNC_FLAG);

    /* ʹ��Y���� */
    Emm_V5_En_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, true, MOTOR_SYNC_FLAG);

    /* ��ʼֹͣ */
    Step_Motor_Stop();
}

/**
 * @brief ����XY�����ٶ�
 * @param x_percent X���ٶȰٷֱȣ���Χ-100��100
 * @param y_percent Y���ٶȰٷֱȣ���Χ-100��100
 */
void Step_Motor_Set_Speed(int8_t x_percent, int8_t y_percent)
{
    uint8_t x_dir, y_dir;
    uint16_t x_speed, y_speed;

    /* ���ưٷֱȷ�Χ */
    if (x_percent > 100)
        x_percent = 100;
    if (x_percent < -100)
        x_percent = -100;
    if (y_percent > 100)
        y_percent = 100;
    if (y_percent < -100)
        y_percent = -100;

    /* ����X�᷽�� */
    if (x_percent >= 0)
    {
        x_dir = 0; /* CW���� */
    }
    else
    {
        x_dir = 1;              /* CCW���� */
        x_percent = -x_percent; /* ȡ����ֵ */
    }

    /* ����Y�᷽�� */
    if (y_percent >= 0)
    {
        y_dir = 0; /* CW���� */
    }
    else
    {
        y_dir = 1;              /* CCW���� */
        y_percent = -y_percent; /* ȡ����ֵ */
    }

    /* ����ʵ���ٶ�ֵ(�ٷֱ�ת��ΪRPM) */
    x_speed = (uint16_t)((x_percent * MOTOR_MAX_SPEED) / 100);
    y_speed = (uint16_t)((y_percent * MOTOR_MAX_SPEED) / 100);

	/* ���ӵ��Դ�ӡȷ�ϼ��� */
     my_printf(&huart1, "X: dir=%d, speed=%u; Y: dir=%d, speed=%u\r\n", x_dir, x_speed, y_dir, y_speed);
	
    /* ����X���� */
    Emm_V5_Vel_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, x_speed, MOTOR_ACCEL, MOTOR_SYNC_FLAG);

    /* ����Y���� */
    Emm_V5_Vel_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, y_speed, MOTOR_ACCEL, MOTOR_SYNC_FLAG);
}

/**
 * @brief ����XY�����ٶ�
 * @param x_rpm X��Ŀ���ٶȣ���λRPM (revolutions per minute)��
 *              ֧�ָ�ֵ��ʾ����
 *              �ٶȾ���Ϊ0.1RPM��С��0.05 RPM�ľ���ֵ��������Ϊ0��
 * @param y_rpm Y��Ŀ���ٶȣ���λRPM (revolutions per minute)��
 *              ֧�ָ�ֵ��ʾ����
 *              �ٶȾ���Ϊ0.1RPM��С��0.05 RPM�ľ���ֵ��������Ϊ0��
 */
void Step_Motor_Set_Speed_my(float x_rpm, float y_rpm)
{
    uint8_t x_dir, y_dir;
    uint16_t x_speed_scaled, y_speed_scaled; // �ٶ�ֵ����λΪ 0.1 RPM
    float abs_x_rpm, abs_y_rpm;

    /* 1. ��������RPM��Χ��ȷ�������������������ٶ� */
    // �������RPMֵǯλ�� [-MOTOR_MAX_SPEED, MOTOR_MAX_SPEED] ֮��
    if (x_rpm > MOTOR_MAX_SPEED)
    {
        x_rpm = MOTOR_MAX_SPEED;
    }
    else if (x_rpm < -MOTOR_MAX_SPEED)
    {
        x_rpm = -MOTOR_MAX_SPEED;
    }

    if (y_rpm > MOTOR_MAX_SPEED)
    {
        y_rpm = MOTOR_MAX_SPEED;
    }
    else if (y_rpm < -MOTOR_MAX_SPEED)
    {
        y_rpm = -MOTOR_MAX_SPEED;
    }

    /* 2. ����X�᷽��ͻ�ȡ�����ٶ� */
    if (x_rpm >= 0.0f)
    {
        x_dir = 0; /* CW���� (��ת) */
        abs_x_rpm = x_rpm;
    }
    else
    {
        x_dir = 1; /* CCW���� (��ת) */
        abs_x_rpm = -x_rpm; /* ȡ����ֵ */
    }

    /* 3. ����Y�᷽��ͻ�ȡ�����ٶ� */
    if (y_rpm >= 0.0f)
    {
        y_dir = 0; /* CW���� (��ת) */
        abs_y_rpm = y_rpm;
    }
    else
    {
        y_dir = 1; /* CCW���� (��ת) */
        abs_y_rpm = -y_rpm; /* ȡ����ֵ */
    }

    /* 4. ����ʵ�ʷ��͸�������������ٶ�ֵ (��λΪ 0.1 RPM) */
    // ��RPMֵ����10���õ���0.1RPMΪ��λ������ֵ��
    // ����0.5f��Ϊ�˽����������롣
    // ���������� 0.04 RPM (0.4 scaled) + 0.5f = 0.9f -> 0 (uint16_t)
    // 0.05 RPM (0.5 scaled) + 0.5f = 1.0f -> 1 (uint16_t)
    x_speed_scaled = (uint16_t)(abs_x_rpm * 10 + 0.5f);
    y_speed_scaled = (uint16_t)(abs_y_rpm * 10 + 0.5f);
    
    // �ٴμ�������� scaled speed �Ƿ񳬳� uint16_t �����ֵ��
    // ������������ǯλ�� (MOTOR_MAX_SPEED * RPM_TO_SCALED_FACTOR) ���ᳬ�� uint16_t��
    // ����Ϊ³���Լ�飬�������Ӵ��С�
//    uint16_t max_scaled_speed = (uint16_t)(MOTOR_MAX_SPEED * 10 + 0.5f);
//    if (x_speed_scaled > max_scaled_speed) {
//        x_speed_scaled = max_scaled_speed;
//    }
//    if (y_speed_scaled > max_scaled_speed) {
//        y_speed_scaled = max_scaled_speed;
//    }


//    /* ���ӵ��Դ�ӡȷ�ϼ��� */
//    my_printf(&huart1, "X: input_rpm=%.1f, dir=%d, abs_rpm=%.1f, scaled_speed=%u; Y: input_rpm=%.1f, dir=%d, abs_rpm=%.1f, scaled_speed=%u\r\n",
//              x_rpm, x_dir, abs_x_rpm, x_speed_scaled,
//              y_rpm, y_dir, abs_y_rpm, y_speed_scaled);
    
    /* ����X���� */
    Emm_V5_Vel_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, x_speed_scaled, MOTOR_ACCEL, MOTOR_SYNC_FLAG);

    /* ����Y���� */
    Emm_V5_Vel_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, y_speed_scaled, MOTOR_ACCEL, MOTOR_SYNC_FLAG);
}

/**
 * @brief ����XY�����ƶ�һ�ξ��루ʹ��λ��ģʽ��
 * @param x_distance X���ƶ����루������������ֵΪCW���򣬸�ֵΪCCW����
 * @param y_distance Y���ƶ����루������������ֵΪCW���򣬸�ֵΪCCW����
 */
void Step_Motor_Set_Pwm(int32_t x_distance, int32_t y_distance)
{
    uint8_t x_dir, y_dir;
    uint32_t x_clk, y_clk;
    uint16_t speed = MOTOR_MAX_SPEED;  /* ʹ������ٶȣ��������Ҫ���� */
    uint8_t acc = MOTOR_ACCEL;        /* ʹ��Ԥ������ٶ� */

    /* ����X�᷽��������� */
    if (x_distance >= 0)
    {
        x_dir = 0; /* CW���� */
        x_clk = (uint32_t)x_distance;
    }
    else
    {
        x_dir = 1; /* CCW���� */
        x_clk = (uint32_t)(-x_distance); /* ȡ����ֵ */
    }

    /* ����Y�᷽��������� */
    if (y_distance >= 0)
    {
        y_dir = 0; /* CW���� */
        y_clk = (uint32_t)y_distance;
    }
    else
    {
        y_dir = 1; /* CCW���� */
        y_clk = (uint32_t)(-y_distance); /* ȡ����ֵ */
    }

    /* ����X����������˶��������þ���ģʽ�� */
    Emm_V5_Pos_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, speed, acc, x_clk, false, MOTOR_SYNC_FLAG);

    /* ����Y����������˶��������þ���ģʽ�� */
    Emm_V5_Pos_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, speed, acc, y_clk, false, MOTOR_SYNC_FLAG);
}


/**
 * @brief ֹͣ���е��
 */
void Step_Motor_Stop(void)
{
    /* ֹͣX���� */
    Emm_V5_Stop_Now(&MOTOR_X_UART, MOTOR_X_ADDR, MOTOR_SYNC_FLAG);

    /* ֹͣY���� */
    Emm_V5_Stop_Now(&MOTOR_Y_UART, MOTOR_Y_ADDR, MOTOR_SYNC_FLAG);
}

void step_motor_proc(void)
{

}

/**
 * @brief 电机测试任务
 * @details 按照指定顺序测试电机运动：
 *          1. X轴正方向 10RPM，持续3秒
 *          2. Y轴正方向 10RPM，持续3秒
 *          3. X轴负方向 -10RPM，持续3秒
 *          4. Y轴负方向 -10RPM，持续3秒
 *          5. 停止运动
 */
void motor_test_proc(void)
{
    static uint8_t test_state = 0;        // 测试状态机
    static uint32_t state_start_time = 0; // 当前状态开始时间
    static uint8_t test_enabled = 0;      // 测试使能标志
    uint32_t current_time = HAL_GetTick();
    const uint32_t STATE_DURATION = 3000; // 每个状态持续3秒

    // 检查是否需要开始测试（可以通过某种条件触发）
    // 这里简单地在系统启动10秒后自动开始测试
    if (!test_enabled && current_time > 10000) {
        test_enabled = 1;
        test_state = 1;
        state_start_time = current_time;
  //      my_printf(&huart1, "电机测试开始...\r\n");
    }

    // 如果测试未使能，直接返回
    if (!test_enabled) {
        return;
    }

    // 状态机处理
    switch (test_state) {
        case 1: // X轴正方向运动
            if (current_time - state_start_time >= STATE_DURATION) {
                // 状态切换
                test_state = 2;
                state_start_time = current_time;
                my_printf(&huart1, "测试阶段2: Y轴正方向运动\r\n");
            } else {
                // 执行X轴正方向运动
                Step_Motor_Set_Speed_my(10, 0);
                if (current_time - state_start_time < 100) { // 只在开始时打印一次
                    my_printf(&huart1, "测试阶段1: X轴正方向运动 (10, 0)\r\n");
                }
            }
            break;

        case 2: // Y轴正方向运动
            if (current_time - state_start_time >= STATE_DURATION) {
                // 状态切换
                test_state = 3;
                state_start_time = current_time;
                my_printf(&huart1, "测试阶段3: X轴负方向运动\r\n");
            } else {
                // 执行Y轴正方向运动
                Step_Motor_Set_Speed_my(0, 10);
                if (current_time - state_start_time < 100) { // 只在开始时打印一次
                    my_printf(&huart1, "测试阶段2: Y轴正方向运动 (0, 10)\r\n");
                }
            }
            break;

        case 3: // X轴负方向运动
            if (current_time - state_start_time >= STATE_DURATION) {
                // 状态切换
                test_state = 4;
                state_start_time = current_time;
                my_printf(&huart1, "测试阶段4: Y轴负方向运动\r\n");
            } else {
                // 执行X轴负方向运动
                Step_Motor_Set_Speed_my(-10, 0);
                if (current_time - state_start_time < 100) { // 只在开始时打印一次
                    my_printf(&huart1, "测试阶段3: X轴负方向运动 (-10, 0)\r\n");
                }
            }
            break;

        case 4: // Y轴负方向运动
            if (current_time - state_start_time >= STATE_DURATION) {
                // 状态切换到停止
                test_state = 5;
                state_start_time = current_time;
                my_printf(&huart1, "测试阶段5: 停止运动\r\n");
            } else {
                // 执行Y轴负方向运动
                Step_Motor_Set_Speed_my(0, -10);
                if (current_time - state_start_time < 100) { // 只在开始时打印一次
                    my_printf(&huart1, "测试阶段4: Y轴负方向运动 (0, -10)\r\n");
                }
            }
            break;

        case 5: // 停止运动
            Step_Motor_Set_Speed_my(0, 0);
//            if (current_time - state_start_time < 100) { // 只在开始时打印一次
//                my_printf(&huart1, "电机测试完成，停止运动 (0, 0)\r\n");
//            }
            // 测试完成后可以选择重新开始或保持停止状态
            if (current_time - state_start_time >= 2000) { // 停止2秒后重新开始
                test_state = 1;
                state_start_time = current_time;
               
            }
            break;

        default:
            test_state = 0;
            test_enabled = 0;
            break;
    }
}

