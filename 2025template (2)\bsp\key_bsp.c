#include "key_bsp.h"
#include "oled_bsp.h"  // 包含OLED相关声明

uint8_t key_val = 0;
uint8_t key_old = 0;
uint8_t key_down = 0;
uint8_t key_up = 0;

// 添加task和sure变量
uint8_t task = 0;
uint8_t sure = 0;

uint8_t key_read(void)
{
	uint8_t temp = 0;
	
//	if(HAL_GPIO_ReadPin(KEY1_GPIO_Port,KEY1_Pin) == GPIO_PIN_RESET)
	if(HAL_GPIO_ReadPin(GPIOA,GPIO_PIN_0) == GPIO_PIN_RESET)
		temp = 1;
	if(HAL_GPIO_ReadPin(KEY2_GPIO_Port,KEY2_Pin) == GPIO_PIN_RESET)
		temp = 2;
	if(HAL_GPIO_ReadPin(KEY3_GPIO_Port,KEY3_Pin) == GPIO_PIN_RESET)
		temp = 3;
	if(HAL_GPIO_ReadPin(KEY4_GPIO_Port,KEY4_Pin) == GPIO_PIN_RESET)
		temp = 4;
	return temp;
}

void key_proc(void)
{
	key_val = key_read();
	key_down = key_val & (key_val ^ key_old);
	key_up = ~key_val & (key_val ^ key_old);
	key_old = key_val;
	if(key_down==1)
	{
		// 按键1：task变量加1
		task++;
		if(task > 99) task = 0;  // 防止溢出，超过99时重置为0
		my_printf(&huart1, "Key 1 pressed! task: %d\r\n", task);
	}

	if(key_down==2)
	{
		// 按键2：sure变量加1
		sure++;
		if(sure > 99) sure = 0;  // 防止溢出，超过99时重置为0
		my_printf(&huart1, "Key 2 pressed! sure: %d\r\n", sure);
	}

	// 当按键3按下时，OLED显示的数字加一
	if(key_down==3)
	{
		oled_display_number++;
	
		// 防止数字过大，可以设置一个上限
		if(oled_display_number > 999)
		{
			oled_display_number = 0;  // 超过999时重置为0
		}
	}
}
